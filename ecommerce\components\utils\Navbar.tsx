"use client";
import {
  ChevronDown,
  Menu,
  Search,
  ShoppingCart,
  Trash2,
  User,
  X,
} from "lucide-react";
import React, { useEffect, useRef } from "react";
import CartMenu from "./CartManu";
import MyAccountMenu from "./MyAccountMenu";
import CategoriesMenu from "./CategoriesMenu";
import Link from "next/link";
import Image from "next/image";
import { Button } from "../ui/button";
import { SearchBtn } from "./SearchBtn";
import { ThemeToggle } from "../ui/theme-toggle";
import { useSession } from "next-auth/react";

const NavBar = () => {
  const { status } = useSession();
  const [isOpen, setIsOpen] = React.useState({
    humburgar: false,
    cart: false,
    search: false,
    account: false,
  });

  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const hamburgerButtonRef = useRef<HTMLButtonElement>(null);

  const quickLinks = [
    { text: "Shop", href: "/shop" },
    { text: "Cart", href: "/cart" },
    { text: "Account", href: "/account" },
    // { text: "Categories", href: "/categories" },
  ];

  // Handle click outside to close mobile menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (
        isOpen.humburgar &&
        mobileMenuRef.current &&
        hamburgerButtonRef.current &&
        !mobileMenuRef.current.contains(event.target as Node) &&
        !hamburgerButtonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(prev => ({ ...prev, humburgar: false }));
      }
    };

    if (isOpen.humburgar) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside as EventListener);
      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside as EventListener);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen.humburgar]);

  // Handle escape key to close mobile menu
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen.humburgar) {
        setIsOpen(prev => ({ ...prev, humburgar: false }));
      }
    };

    if (isOpen.humburgar) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen.humburgar]);

  return (
    <nav className="sticky top-0 z-50 bg-theme-header text-white backdrop-blur-sm shadow-md w-full">
      <div className="w-full py-3 px-4 lg:px-8">
        <div className="flex items-center justify-between max-w-screen-2xl mx-auto">
          <div className="flex items-center space-x-8">
            <div className="shrink-0">
              <Link href="/" className="flex items-center gap-2">
                <Image
                  src="/logotriumph.png"
                  alt="Triumph Enterprises Logo"
                  width={32}
                  height={32}
                  className="h-8 w-auto"
                  priority
                />
                <span className="text-[10px] md:text-xl lg:text-2xl font-bold text-white">TRIUMPH ENTERPRISES</span>
              </Link>
            </div>
            <div className="hidden md:flex space-x-6">
              <Link href="/shop" className="text-white hover:text-theme-accent-primary font-medium transition-colors relative group">
                Shop
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-theme-accent-secondary transition-all duration-300 group-hover:w-full"></span>
              </Link>
              {/* <Link href="/categories" className="text-gray-700 hover:text-indigo-600 font-medium transition-colors">
                Categories
              </Link> */}
            </div>
          </div>

          <div className="flex items-center gap-1 lg:gap-3">
            <div className="">
              <Button
                variant="ghost"
                onClick={() => {
                  setIsOpen((val) => {
                    return { ...val, search: !val.search };
                  });
                }}
                className="inline-flex items-center rounded-full justify-center p-3 md:p-2 hover:bg-white/10 text-sm font-medium leading-none text-white transition-all duration-200 active:scale-95 mobile-touch-target"
                aria-label="Search"
              >
                <Search className="h-5 w-5" />
              </Button>

              {isOpen.search && (
                <div className="z-50 backdrop-blur-lg bg-black/50 fixed top-0 left-0 h-screen w-full flex justify-center items-center overflow-y-scroll">
                  {/* Enhanced Close Button for Search */}
                  <Button
                    variant="ghost"
                    onClick={() => setIsOpen({ ...isOpen, search: false })}
                    className="absolute top-4 right-4 h-12 w-12 p-0 rounded-full bg-white/10 hover:bg-white/20 text-white hover:text-theme-accent-primary transition-all duration-200 active:scale-95 z-60"
                    aria-label="Close search"
                  >
                    <X className="h-6 w-6" />
                  </Button>
                  <SearchBtn />
                </div>
              )}
            </div>

            {/* Theme Toggle */}
            <ThemeToggle />

            <div className="relative">
              <Button
                variant="ghost"
                onClick={() => {
                  if (status === "authenticated") {
                    setIsOpen((val) => {
                      return { ...val, cart: !val.cart };
                    });
                  } else {
                    // Redirect to login if not authenticated
                    window.location.href = "/auth/login?callbackUrl=/cart";
                  }
                }}
                className="inline-flex items-center rounded-full justify-center p-3 md:p-2 hover:bg-white/10 text-sm font-medium leading-none text-white transition-all duration-200 active:scale-95 mobile-touch-target"
                aria-label="Shopping cart"
              >
                <ShoppingCart className="w-5 h-5" />
              </Button>

              {isOpen.cart && status === "authenticated" && (
                <>
                  {/* Mobile backdrop for cart */}
                  <div
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden"
                    onClick={() => setIsOpen({ ...isOpen, cart: false })}
                  />
                  <div
                    onMouseLeave={() => setIsOpen({ ...isOpen, cart: false })}
                    className="fixed md:absolute h-auto max-h-[calc(100vh-150px)] overflow-y-auto top-[72px] md:top-[45px] right-0 md:right-0 z-50 w-full md:w-auto max-w-full md:max-w-md transition-all duration-300 ease-out"
                  >
                    {/* Mobile Close Button for Cart */}
                    <div className="md:hidden flex justify-end p-3 bg-white border-b border-gray-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsOpen({ ...isOpen, cart: false })}
                        className="h-8 w-8 p-0 rounded-full hover:bg-gray-100 transition-colors duration-200"
                        aria-label="Close cart"
                      >
                        <X className="h-4 w-4 text-gray-600" />
                      </Button>
                    </div>
                    <div className="p-2 md:p-2">
                      <CartMenu key={`cart-menu-${isOpen.cart}`} />
                    </div>
                  </div>
                </>
              )}
            </div>
            <div className="relative">
              <Button
                variant="ghost"
                onClick={() =>
                  setIsOpen({ ...isOpen, account: !isOpen.account })
                }
                className="inline-flex items-center rounded-full justify-center p-3 md:p-2 hover:bg-white/10 text-sm font-medium leading-none text-white transition-all duration-200 active:scale-95 mobile-touch-target"
                aria-label="User account"
              >
                <User className="w-5 h-5" />
              </Button>

              {isOpen.account && (
                <>
                  {/* Mobile backdrop for account menu */}
                  <div
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden"
                    onClick={() => setIsOpen({ ...isOpen, account: false })}
                  />
                  <div
                    onMouseLeave={() => setIsOpen({ ...isOpen, account: false })}
                    id="userDropdown1"
                    className="fixed md:absolute top-[72px] md:top-[45px] right-0 md:right-0 z-50 w-full md:w-auto max-w-full md:max-w-md shadow-md bg-white rounded-md transition-all duration-300 ease-out"
                  >
                    {/* Mobile Close Button for Account Menu */}
                    <div className="md:hidden flex justify-end p-3 border-b border-gray-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsOpen({ ...isOpen, account: false })}
                        className="h-8 w-8 p-0 rounded-full hover:bg-gray-100 transition-colors duration-200"
                        aria-label="Close account menu"
                      >
                        <X className="h-4 w-4 text-gray-600" />
                      </Button>
                    </div>
                    <MyAccountMenu />
                  </div>
                </>
              )}
            </div>
            <Button
              ref={hamburgerButtonRef}
              variant="ghost"
              type="button"
              onClick={() =>
                setIsOpen({ ...isOpen, humburgar: !isOpen.humburgar })
              }
              data-collapse-toggle="ecommerce-navbar-menu-1"
              aria-controls="ecommerce-navbar-menu-1"
              aria-expanded={isOpen.humburgar}
              aria-label={isOpen.humburgar ? "Close menu" : "Open menu"}
              className="md:hidden inline-flex items-center justify-center hover:bg-white/10 rounded-full p-3 text-white transition-all duration-200 active:scale-95"
            >
              <div className="relative w-5 h-5">
                <Menu
                  className={`w-5 h-5 absolute transition-all duration-300 ${
                    isOpen.humburgar ? 'opacity-0 rotate-180 scale-75' : 'opacity-100 rotate-0 scale-100'
                  }`}
                />
                <X
                  className={`w-5 h-5 absolute transition-all duration-300 ${
                    isOpen.humburgar ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-180 scale-75'
                  }`}
                />
              </div>
            </Button>
          </div>
        </div>

        {/* Mobile Menu Backdrop */}
        <div
          className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden transition-all duration-300 ${
            isOpen.humburgar
              ? 'opacity-100 visible'
              : 'opacity-0 invisible pointer-events-none'
          }`}
          onClick={() => setIsOpen({ ...isOpen, humburgar: false })}
        />

        {/* Mobile Menu */}
        <div
          ref={mobileMenuRef}
          className={`fixed top-[72px] left-0 right-0 bg-theme-header text-white z-50 md:hidden border-t border-gray-700/30 shadow-2xl transition-all duration-300 ease-out ${
            isOpen.humburgar
              ? 'translate-y-0 opacity-100 visible'
              : '-translate-y-full opacity-0 invisible'
          }`}
          style={{
            willChange: 'transform, opacity',
            maxHeight: 'calc(100vh - 72px)',
            overflowY: 'auto'
          }}
        >
          <div className="px-6 py-6 space-y-6">
            {/* Mobile Menu Header with Close Button */}
            <div className="flex items-center justify-between pb-4 border-b border-gray-700/30">
              <h2 className="text-xl font-bold text-white">Menu</h2>
              <Button
                variant="ghost"
                onClick={() => setIsOpen({ ...isOpen, humburgar: false })}
                className="h-10 w-10 p-0 rounded-full hover:bg-white/10 text-white transition-all duration-200 active:scale-95"
                aria-label="Close menu"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
            {/* Main Navigation Items */}
            <div className="space-y-3">
              
              {[
                { href: "/shop", icon: ShoppingCart, label: "Shop", color: "theme-accent-primary", description: "Browse our products" },
                { href: "/cart", icon: ShoppingCart, label: "Cart", color: "theme-accent-primary", description: "View your items" },
                { href: "/account", icon: User, label: "Account", color: "theme-accent-secondary", description: "Manage your profile" }
              ].map((item, index) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`group flex items-center space-x-4 p-5 rounded-2xl bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 transition-all duration-300 active:scale-95 shadow-lg hover:shadow-xl ${
                    isOpen.humburgar ? 'animate-slide-in-mobile' : ''
                  }`}
                  style={{
                    animationDelay: `${index * 100}ms`,
                    animationFillMode: 'both'
                  }}
                  onClick={() => setIsOpen({ ...isOpen, humburgar: false })}
                >
                  <div className={`w-14 h-14 bg-gradient-to-br from-${item.color}/30 to-${item.color}/10 rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}>
                    <item.icon className={`h-7 w-7 text-${item.color} group-hover:text-white transition-colors duration-300`} />
                  </div>
                  <div className="flex-1">
                    <span className="font-bold text-xl text-white group-hover:text-theme-accent-primary transition-colors duration-300">{item.label}</span>
                    <p className="text-sm text-gray-300 group-hover:text-gray-200 transition-colors duration-300">{item.description}</p>
                  </div>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <ChevronDown className="h-5 w-5 text-white rotate-[-90deg]" />
                  </div>
                </Link>
              ))}
            </div>

            {/* Divider */}
            <div className="border-t border-gray-700/30 my-6" />

            {/* Quick Links */}
            <div className="space-y-4">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center">
                <span className="w-2 h-2 bg-theme-accent-primary rounded-full mr-3"></span>
                Quick Access
              </h3>
              <div className="grid grid-cols-2 gap-4">
                {quickLinks.map(({ text, href }, index) => (
                  <Link
                    key={text}
                    href={href}
                    className={`group text-center py-4 px-4 rounded-xl bg-gradient-to-br from-white/10 to-white/5 hover:from-theme-accent-primary/20 hover:to-theme-accent-primary/10 text-gray-200 hover:text-white border border-white/10 hover:border-theme-accent-primary/30 transition-all duration-300 active:scale-95 shadow-lg hover:shadow-xl ${
                      isOpen.humburgar ? 'animate-slide-in-mobile' : ''
                    }`}
                    style={{
                      animationDelay: `${(index + 3) * 100}ms`,
                      animationFillMode: 'both'
                    }}
                    onClick={() => setIsOpen({ ...isOpen, humburgar: false })}
                  >
                    <span className="text-sm font-semibold group-hover:text-theme-accent-primary transition-colors duration-300">{text}</span>
                  </Link>
                ))}
              </div>
            </div>

            {/* Bottom spacing for safe area */}
            <div className="h-4" />
          </div>
        </div>
      </div>
    </nav>
  );
};

export default NavBar;
