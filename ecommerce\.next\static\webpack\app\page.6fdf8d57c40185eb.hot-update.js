"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ui/TrustIndicators.tsx":
/*!*******************************************!*\
  !*** ./components/ui/TrustIndicators.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Clock_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Clock,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/badge-check.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Clock_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Clock,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Clock_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Clock,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Clock_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Clock,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n\n\n\nconst TrustIndicators = ()=>{\n    const items = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Clock_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"w-5 h-5 xs:w-6 xs:h-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                lineNumber: 7,\n                columnNumber: 13\n            }, undefined),\n            title: \"Quality Assured\",\n            description: \"Tested for excellence\",\n            gradient: \"from-theme-accent-primary to-theme-accent-hover\",\n            delay: 0\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Clock_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5 xs:w-6 xs:h-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                lineNumber: 14,\n                columnNumber: 13\n            }, undefined),\n            title: \"Easy Returns\",\n            description: \"7-day return policy\",\n            gradient: \"from-theme-accent-secondary to-theme-accent-primary\",\n            delay: 0.1\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Clock_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-5 h-5 xs:w-6 xs:h-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                lineNumber: 21,\n                columnNumber: 13\n            }, undefined),\n            title: \"Secure Checkout\",\n            description: \"100% protected payments\",\n            gradient: \"from-theme-accent-primary to-theme-accent-secondary\",\n            delay: 0.2\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Clock_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-5 h-5 xs:w-6 xs:h-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                lineNumber: 28,\n                columnNumber: 13\n            }, undefined),\n            title: \"24/7 Support\",\n            description: \"Always here to help you\",\n            gradient: \"from-theme-accent-secondary to-theme-accent-hover\",\n            delay: 0.3\n        }\n    ];\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.15,\n                delayChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-6 sm:py-8 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/2 left-1/4 w-20 h-20 bg-theme-accent-primary/5 rounded-full blur-xl opacity-60\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/3 right-1/4 w-16 h-16 bg-theme-accent-secondary/5 rounded-full blur-xl opacity-60\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                className: \"grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 xs:gap-4 sm:gap-6 max-w-6xl mx-auto relative z-10\",\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: \"visible\",\n                viewport: {\n                    once: true,\n                    amount: 0.3\n                },\n                children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: itemVariants,\n                        custom: index,\n                        className: \"group flex flex-col items-center text-center p-3 xs:p-4 sm:p-6 rounded-xl bg-card/95 backdrop-blur-sm shadow-md border border-border/50   hover:shadow-lg transition-all duration-300 hover:-translate-y-1 hover:bg-card hover:border-theme-accent-primary/30 hover:shadow-theme-accent-primary/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 xs:p-3 rounded-full bg-gradient-to-r \".concat(item.gradient, \" text-white mb-2 xs:mb-4 shadow-md\\n              transition-all duration-300 group-hover:scale-110 group-hover:shadow-lg group-hover:shadow-theme-accent-primary/25 relative overflow-hidden\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-base xs:text-lg text-card-foreground mb-1 xs:mb-2 transition-all duration-300 group-hover:text-theme-accent-primary\",\n                                children: item.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs xs:text-sm text-muted-foreground transition-all duration-300 group-hover:text-card-foreground\",\n                                children: item.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\TrustIndicators.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TrustIndicators;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TrustIndicators);\nvar _c;\n$RefreshReg$(_c, \"TrustIndicators\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/TrustIndicators.tsx\n"));

/***/ })

});