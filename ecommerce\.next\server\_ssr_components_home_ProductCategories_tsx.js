"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_components_home_ProductCategories_tsx";
exports.ids = ["_ssr_components_home_ProductCategories_tsx"];
exports.modules = {

/***/ "(ssr)/./components/home/<USER>":
/*!***********************************************!*\
  !*** ./components/home/<USER>
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(ssr)/./components/ui/skeleton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst ProductCategories = ({ categories, title = \"Shop by Category\", subtitle, accentColor = \"primary\", variant = \"section\", showTitle = true, showViewAll = true, maxCategories = 12 })=>{\n    const pathName = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isLoading, setIsLoading] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(true);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ProductCategories.useEffect\": ()=>{\n            // Set loading to false when categories are available\n            if (categories && Array.isArray(categories)) {\n                setIsLoading(false);\n            }\n        }\n    }[\"ProductCategories.useEffect\"], [\n        categories\n    ]);\n    // Default categories if none are provided or if the array is empty\n    const defaultCategories = [\n        {\n            id: 1,\n            name: \"Smart Locks\",\n            slug: \"smart-locks\",\n            image_url: \"https://placehold.co/400x400/2ECC71/FFFFFF?text=Smart+Locks\"\n        },\n        {\n            id: 2,\n            name: \"Security Cameras\",\n            slug: \"security-cameras\",\n            image_url: \"https://placehold.co/400x400/3498DB/FFFFFF?text=Security+Cameras\"\n        },\n        {\n            id: 3,\n            name: \"Home Automation\",\n            slug: \"home-automation\",\n            image_url: \"https://placehold.co/400x400/9B59B6/FFFFFF?text=Home+Automation\"\n        },\n        {\n            id: 4,\n            name: \"Lighting\",\n            slug: \"lighting\",\n            image_url: \"https://placehold.co/400x400/F1C40F/FFFFFF?text=Lighting\"\n        },\n        {\n            id: 5,\n            name: \"Sensors\",\n            slug: \"sensors\",\n            image_url: \"https://placehold.co/400x400/E74C3C/FFFFFF?text=Sensors\"\n        },\n        {\n            id: 6,\n            name: \"Alarms\",\n            slug: \"alarms\",\n            image_url: \"https://placehold.co/400x400/1ABC9C/FFFFFF?text=Alarms\"\n        }\n    ];\n    // Show skeleton loader when categories are loading\n    if (isLoading && variant === \"navigation\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"w-full bg-white/95 backdrop-blur-sm border-b border-gray-100 shadow-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"block md:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4 overflow-x-auto scrollbar-hide pb-2 -mx-3 px-3\",\n                            children: Array.from({\n                                length: 8\n                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex flex-col items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 rounded-2xl bg-gray-200 animate-pulse mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                            className: \"w-12 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:block space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-3 lg:gap-4\",\n                                children: Array.from({\n                                    length: 12\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center fade-in-professional\",\n                                        style: {\n                                            animationDelay: `${index * 0.05}s`\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 lg:w-18 lg:h-18 xl:w-20 xl:h-20 rounded-2xl skeleton-professional mb-2 border border-gray-100/80 shadow-md\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 lg:w-14 xl:w-16 h-3 rounded skeleton-professional\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-3 rounded skeleton-professional opacity-60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 h-px bg-gray-200\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1\",\n                                        children: Array.from({\n                                            length: 8\n                                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 lg:w-14 lg:h-14 rounded-xl skeleton-professional mb-1 border border-gray-100/60 shadow-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 lg:w-10 h-2 rounded skeleton-professional\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Don't render navigation if no categories\n    if (!categories || !Array.isArray(categories) || categories.length === 0) {\n        if (variant === \"navigation\") {\n            return null;\n        }\n    }\n    // Use provided categories if available, otherwise use default categories\n    // Make sure we have a valid array to work with\n    const effectiveCategories = Array.isArray(categories) && categories.length > 0 ? categories : defaultCategories;\n    // Process categories to ensure proper image handling\n    const categoriesWithImages = effectiveCategories.map((category)=>{\n        // Use image_url from backend API if available, otherwise fallback to image field or generate placeholder\n        let imageUrl = category.image_url || category.image;\n        // If no image is available from the backend, generate a colored placeholder immediately\n        if (!imageUrl) {\n            const colors = [\n                '2ECC71',\n                '3498DB',\n                '9B59B6',\n                'F1C40F',\n                'E74C3C',\n                '1ABC9C',\n                'E67E22',\n                '34495E',\n                '95A5A6',\n                'F39C12',\n                'D35400',\n                '8E44AD'\n            ];\n            const colorIndex = category.id % colors.length;\n            const color = colors[colorIndex];\n            const categoryText = encodeURIComponent(category.name);\n            imageUrl = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;\n        }\n        return {\n            ...category,\n            image_url: imageUrl,\n            // Add a fallback image path for onError handler\n            fallbackImage: `/assets/products/product-placeholder.svg`\n        };\n    });\n    // Determine accent color classes\n    const accentClasses = {\n        primary: {\n            bg: \"bg-theme-accent-primary/20\",\n            text: \"text-theme-accent-primary\",\n            line: \"bg-theme-accent-primary\",\n            gradient: \"from-theme-accent-primary/5\",\n            activeBg: \"bg-theme-accent-primary\",\n            activeText: \"text-white\",\n            hoverBg: \"hover:bg-theme-accent-primary/10\"\n        },\n        secondary: {\n            bg: \"bg-theme-accent-secondary/30\",\n            text: \"text-theme-accent-secondary\",\n            line: \"bg-theme-accent-secondary\",\n            gradient: \"from-theme-accent-secondary/5\",\n            activeBg: \"bg-theme-accent-secondary\",\n            activeText: \"text-white\",\n            hoverBg: \"hover:bg-theme-accent-secondary/10\"\n        },\n        tertiary: {\n            bg: \"bg-theme-accent-primary/30\",\n            text: \"text-theme-accent-primary\",\n            line: \"bg-theme-accent-primary\",\n            gradient: \"from-theme-accent-primary/10\",\n            activeBg: \"bg-theme-accent-primary\",\n            activeText: \"text-white\",\n            hoverBg: \"hover:bg-theme-accent-primary/10\"\n        }\n    };\n    // Animation variants\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        show: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        show: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5\n            }\n        }\n    };\n    // Navigation variant - Professional Amazon/Flipkart style layout\n    if (variant === \"navigation\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"w-full bg-white/98 backdrop-blur-md border-b border-gray-200/60 shadow-sm relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-theme-accent-primary via-theme-accent-secondary to-theme-accent-primary opacity-60\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-3 sm:py-4 lg:py-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"block md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 overflow-x-auto scrollbar-hide pb-2 -mx-3 px-3\",\n                                children: categoriesWithImages.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: `/products/categories/${category.slug}?callbackUrl=%2F${pathName}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center transition-all duration-300 group-active:scale-95\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative overflow-hidden rounded-2xl w-16 h-16 bg-gradient-to-br from-gray-50 to-gray-100 shadow-lg transition-all duration-300 group-hover:shadow-xl mb-2 border border-gray-200/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: category.image_url,\n                                                                alt: category.name,\n                                                                className: \"absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\",\n                                                                onError: (e)=>{\n                                                                    const imgElement = e.target;\n                                                                    if (imgElement.src.includes('placehold.co')) {\n                                                                        return;\n                                                                    }\n                                                                    const colors = [\n                                                                        '2ECC71',\n                                                                        '3498DB',\n                                                                        '9B59B6',\n                                                                        'F1C40F',\n                                                                        'E74C3C',\n                                                                        '1ABC9C',\n                                                                        'E67E22',\n                                                                        '34495E',\n                                                                        '95A5A6',\n                                                                        'F39C12',\n                                                                        'D35400',\n                                                                        '8E44AD'\n                                                                    ];\n                                                                    const colorIndex = category.id % colors.length;\n                                                                    const color = colors[colorIndex];\n                                                                    const categoryText = encodeURIComponent(category.name);\n                                                                    imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;\n                                                                    imgElement.onerror = null;\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-gray-700 text-center line-clamp-2 max-w-[4.5rem] leading-tight\",\n                                                        children: category.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, category.id || `category-${index}`, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-3 lg:gap-4\",\n                                    children: categoriesWithImages.slice(0, 12).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group fade-in-professional\",\n                                            style: {\n                                                animationDelay: `${index * 0.05}s`\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: `/products/categories/${category.slug}?callbackUrl=%2F${pathName}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center transition-all duration-300 group-hover:-translate-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative overflow-hidden rounded-2xl w-16 h-16 lg:w-18 lg:h-18 xl:w-20 xl:h-20 bg-card shadow-md group-hover:shadow-lg mb-2 border border-border group-hover:border-theme-accent-primary/30 transition-all duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-1 rounded-xl overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: category.image_url,\n                                                                        alt: category.name,\n                                                                        className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\",\n                                                                        onError: (e)=>{\n                                                                            const imgElement = e.target;\n                                                                            if (imgElement.src.includes('placehold.co')) {\n                                                                                return;\n                                                                            }\n                                                                            const colors = [\n                                                                                '2ECC71',\n                                                                                '3498DB',\n                                                                                '9B59B6',\n                                                                                'F1C40F',\n                                                                                'E74C3C',\n                                                                                '1ABC9C',\n                                                                                'E67E22',\n                                                                                '34495E',\n                                                                                '95A5A6',\n                                                                                'F39C12',\n                                                                                'D35400',\n                                                                                '8E44AD'\n                                                                            ];\n                                                                            const colorIndex = category.id % colors.length;\n                                                                            const color = colors[colorIndex];\n                                                                            const categoryText = encodeURIComponent(category.name);\n                                                                            imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;\n                                                                            imgElement.onerror = null;\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-t from-theme-accent-primary/20 via-transparent to-theme-accent-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-700 group-hover:text-theme-accent-primary transition-colors duration-300 text-center line-clamp-2 max-w-[4rem] lg:max-w-[4.5rem] xl:max-w-[5rem] leading-tight\",\n                                                            children: category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, category.id || `category-${index}`, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, undefined),\n                                categoriesWithImages.length > 12 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                                    children: \"More Categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 h-px bg-gradient-to-r from-gray-200 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1\",\n                                            children: categoriesWithImages.slice(12).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 group\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: `/products/categories/${category.slug}?callbackUrl=%2F${pathName}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center transition-all duration-300 group-hover:-translate-y-0.5\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative overflow-hidden rounded-xl w-12 h-12 lg:w-14 lg:h-14 bg-card shadow-sm group-hover:shadow-md mb-1 border border-border group-hover:border-theme-accent-primary/40 transition-all duration-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0.5 rounded-lg overflow-hidden\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: category.image_url,\n                                                                                alt: category.name,\n                                                                                className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\",\n                                                                                onError: (e)=>{\n                                                                                    const imgElement = e.target;\n                                                                                    if (imgElement.src.includes('placehold.co')) {\n                                                                                        return;\n                                                                                    }\n                                                                                    const colors = [\n                                                                                        '2ECC71',\n                                                                                        '3498DB',\n                                                                                        '9B59B6',\n                                                                                        'F1C40F',\n                                                                                        'E74C3C',\n                                                                                        '1ABC9C',\n                                                                                        'E67E22',\n                                                                                        '34495E',\n                                                                                        '95A5A6',\n                                                                                        'F39C12',\n                                                                                        'D35400',\n                                                                                        '8E44AD'\n                                                                                    ];\n                                                                                    const colorIndex = category.id % colors.length;\n                                                                                    const color = colors[colorIndex];\n                                                                                    const categoryText = encodeURIComponent(category.name);\n                                                                                    imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;\n                                                                                    imgElement.onerror = null;\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 bg-theme-accent-primary/15 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-muted-foreground group-hover:text-theme-accent-primary transition-colors duration-300 text-center line-clamp-2 max-w-[3rem] lg:max-w-[3.5rem] leading-tight\",\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, category.id || `category-more-${index}`, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Section variant - full layout with background and decorations\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-12 sm:py-16 md:py-20 relative overflow-hidden w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 bg-gradient-to-b ${accentClasses[accentColor].gradient} to-theme-homepage z-0 opacity-70`\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/3 right-[15%] w-32 h-32 rounded-full bg-theme-accent-secondary/10 blur-xl opacity-50\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 385,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1/3 left-[10%] w-24 h-24 rounded-full bg-theme-accent-primary/10 blur-xl opacity-50\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 w-full max-w-full 2xl:max-w-[1536px]\",\n                children: [\n                    showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center mb-8 sm:mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-foreground mb-3 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative z-10\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `absolute -bottom-1 left-0 right-0 h-3 ${accentClasses[accentColor].bg} transform -rotate-1 z-0`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, undefined),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-center max-w-2xl mb-4\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 26\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-16 sm:w-24 h-1 ${accentClasses[accentColor].line} rounded-full mt-1`\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"show\",\n                        viewport: {\n                            once: true,\n                            margin: \"-100px\"\n                        },\n                        className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6\",\n                        children: categoriesWithImages.slice(0, maxCategories).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                variants: itemVariants,\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: `/products/categories/${category.slug}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative overflow-hidden rounded-xl aspect-square bg-muted shadow-md transition-all duration-300 group-hover:shadow-lg group-hover:shadow-theme-accent-primary/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/50 opacity-70 z-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: category.image_url,\n                                                alt: category.name,\n                                                className: \"absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\",\n                                                onError: (e)=>{\n                                                    // Fallback to a colored placeholder if the backend image fails\n                                                    const imgElement = e.target;\n                                                    // Prevent infinite error loops\n                                                    if (imgElement.src.includes('placehold.co')) {\n                                                        return;\n                                                    }\n                                                    // Generate a colored placeholder based on category name\n                                                    const colors = [\n                                                        '2ECC71',\n                                                        '3498DB',\n                                                        '9B59B6',\n                                                        'F1C40F',\n                                                        'E74C3C',\n                                                        '1ABC9C',\n                                                        'E67E22',\n                                                        '34495E',\n                                                        '95A5A6',\n                                                        'F39C12',\n                                                        'D35400',\n                                                        '8E44AD'\n                                                    ];\n                                                    const colorIndex = category.id % colors.length;\n                                                    const color = colors[colorIndex];\n                                                    const categoryText = encodeURIComponent(category.name);\n                                                    imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;\n                                                    imgElement.onerror = null; // Prevent further error handling\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-x-0 bottom-0 p-3 z-20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-medium text-sm sm:text-base text-shadow\",\n                                                    children: category.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-theme-accent-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, category.id || `category-${index}`, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, undefined),\n                    showViewAll && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/shop\",\n                            className: `flex items-center ${accentClasses[accentColor].text} hover:text-theme-accent-hover group transition-all duration-300`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"View All Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n        lineNumber: 380,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductCategories);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/home/<USER>");

/***/ }),

/***/ "(ssr)/./components/ui/skeleton.tsx":
/*!************************************!*\
  !*** ./components/ui/skeleton.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\nfunction Skeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3NrZWxldG9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFvQztBQUVwQyxTQUFTQyxTQUFTLEVBQ2hCQyxTQUFTLEVBQ1QsR0FBR0MsT0FDa0M7SUFDckMscUJBQ0UsOERBQUNDO1FBQ0NGLFdBQVdGLDhDQUFFQSxDQUFDLHFDQUFxQ0U7UUFDbEQsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFbUIiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXGNvbXBvbmVudHNcXHVpXFxza2VsZXRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY24gfSBmcm9tIFwiLi4vLi4vbGliL3V0aWxzXCJcclxuXHJcbmZ1bmN0aW9uIFNrZWxldG9uKHtcclxuICBjbGFzc05hbWUsXHJcbiAgLi4ucHJvcHNcclxufTogUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXZcclxuICAgICAgY2xhc3NOYW1lPXtjbihcImFuaW1hdGUtcHVsc2Ugcm91bmRlZC1tZCBiZy1tdXRlZFwiLCBjbGFzc05hbWUpfVxyXG4gICAgICB7Li4ucHJvcHN9XHJcbiAgICAvPlxyXG4gIClcclxufVxyXG5cclxuZXhwb3J0IHsgU2tlbGV0b24gfVxyXG4iXSwibmFtZXMiOlsiY24iLCJTa2VsZXRvbiIsImNsYXNzTmFtZSIsInByb3BzIiwiZGl2Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/skeleton.tsx\n");

/***/ })

};
;