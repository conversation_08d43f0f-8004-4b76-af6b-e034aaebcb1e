"use client";

import React, { useRef } from "react";
import { motion, useInView, useScroll, useTransform } from "framer-motion";

interface ScrollAnimationWrapperProps {
  children: React.ReactNode;
  className?: string;
  animationType?: "fadeUp" | "fadeLeft" | "fadeRight" | "scale" | "parallax" | "stagger";
  delay?: number;
  duration?: number;
  parallaxOffset?: number;
}

const ScrollAnimationWrapper: React.FC<ScrollAnimationWrapperProps> = ({
  children,
  className = "",
  animationType = "fadeUp",
  delay = 0,
  duration = 0.6,
  parallaxOffset = 50,
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [parallaxOffset, -parallaxOffset]);

  const getAnimationVariants = () => {
    switch (animationType) {
      case "fadeUp":
        return {
          hidden: { opacity: 0, y: 60 },
          visible: { 
            opacity: 1, 
            y: 0,
            transition: { duration, delay, ease: "easeOut" }
          }
        };
      case "fadeLeft":
        return {
          hidden: { opacity: 0, x: -60 },
          visible: { 
            opacity: 1, 
            x: 0,
            transition: { duration, delay, ease: "easeOut" }
          }
        };
      case "fadeRight":
        return {
          hidden: { opacity: 0, x: 60 },
          visible: { 
            opacity: 1, 
            x: 0,
            transition: { duration, delay, ease: "easeOut" }
          }
        };
      case "scale":
        return {
          hidden: { opacity: 0, scale: 0.8 },
          visible: { 
            opacity: 1, 
            scale: 1,
            transition: { duration, delay, ease: "easeOut" }
          }
        };
      case "parallax":
        return {
          hidden: { opacity: 0 },
          visible: { 
            opacity: 1,
            transition: { duration, delay }
          }
        };
      case "stagger":
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { 
            opacity: 1, 
            y: 0,
            transition: { 
              duration, 
              delay,
              staggerChildren: 0.1,
              ease: "easeOut"
            }
          }
        };
      default:
        return {
          hidden: { opacity: 0, y: 60 },
          visible: { 
            opacity: 1, 
            y: 0,
            transition: { duration, delay, ease: "easeOut" }
          }
        };
    }
  };

  const variants = getAnimationVariants();

  if (animationType === "parallax") {
    return (
      <motion.div
        ref={ref}
        className={className}
        style={{ y }}
        variants={variants}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
      >
        {children}
      </motion.div>
    );
  }

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={variants}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
    >
      {children}
    </motion.div>
  );
};

export default ScrollAnimationWrapper;
